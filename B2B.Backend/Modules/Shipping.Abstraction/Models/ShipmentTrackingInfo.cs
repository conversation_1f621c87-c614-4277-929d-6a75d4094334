namespace Shipping.Abstraction;

/// <summary>
/// Kargo takip bilgileri
/// </summary>
public class ShipmentTrackingInfo
{
    /// <summary>
    /// Takip numarası
    /// </summary>
    public string TrackingNumber { get; set; } = null!;

    /// <summary>
    /// Kargo anahtarı
    /// </summary>
    public string CargoKey { get; set; } = null!;

    /// <summary>
    /// Mevcut durum
    /// </summary>
    public string Status { get; set; } = null!;

    /// <summary>
    /// Durum açıklaması
    /// </summary>
    public string StatusDescription { get; set; } = null!;

    /// <summary>
    /// Son güncelleme tarihi
    /// </summary>
    public DateTime LastUpdate { get; set; }

    /// <summary>
    /// Tahmini teslimat tarihi
    /// </summary>
    public DateTime? EstimatedDelivery { get; set; }

    /// <summary>
    /// Gerçek teslimat tarihi
    /// </summary>
    public DateTime? ActualDelivery { get; set; }

    /// <summary>
    /// Mevcut konum
    /// </summary>
    public string? CurrentLocation { get; set; }

    /// <summary>
    /// Detaylı takip geçmişi
    /// </summary>
    public List<TrackingEvent> TrackingEvents { get; set; } = new();

    /// <summary>
    /// Ek bilgiler
    /// </summary>
    public string? Notes { get; set; }
}

/// <summary>
/// Takip olayı
/// </summary>
public class TrackingEvent
{
    /// <summary>
    /// Olay tarihi
    /// </summary>
    public DateTime EventDate { get; set; }

    /// <summary>
    /// Olay açıklaması
    /// </summary>
    public string Description { get; set; } = null!;

    /// <summary>
    /// Konum
    /// </summary>
    public string? Location { get; set; }

    /// <summary>
    /// Durum kodu
    /// </summary>
    public string? StatusCode { get; set; }
}
