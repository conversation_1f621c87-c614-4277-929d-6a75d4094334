using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Core.Entities;

[Table("ShippingCarriers")]
public partial class ShippingCarrier : BaseEntity
{
    /// <summary>
    /// Statik ID - Modül tarafından tanımlanır (1: Yurtiçi, 2: MNG, 3: Aras, vb.)
    /// </summary>
    public int StaticId { get; set; }

    /// <summary>
    /// Kargo firması adı (örn: "Yurtiçi Kargo")
    /// </summary>
    [Required, MaxLength(100)]
    public string Name { get; set; } = null!;

    /// <summary>
    /// Kısa kod (örn: "YURTICI", "MNG", "ARAS")
    /// </summary>
    [Required, MaxLength(20)]
    public string ShortCode { get; set; } = null!;

    /// <summary>
    /// Açıklama
    /// </summary>
    [MaxLength(500)]
    public string? Description { get; set; }

    /// <summary>
    /// Modül implementasyonu mevcut mu?
    /// </summary>
    public bool IsImplemented { get; set; } = false;

    /// <summary>
    /// API URL (opsiyonel)
    /// </summary>
    [MaxLength(500)]
    public string? ApiUrl { get; set; }

    /// <summary>
    /// API anahtarı (opsiyonel, şifrelenmiş olarak saklanacak)
    /// </summary>
    [MaxLength(500)]
    public string? ApiKey { get; set; }

    /// <summary>
    /// JSON formatında ek ayarlar
    /// </summary>
    public string? Settings { get; set; }

    /// <summary>
    /// Logo URL'i (opsiyonel)
    /// </summary>
    [MaxLength(500)]
    public string? LogoUrl { get; set; }

    /// <summary>
    /// Takip URL'i (opsiyonel) - {trackingKey} placeholder ile kullanılır
    /// Örnek: "https://www.yurtici.com.tr/tr/takip-sorgula?code={trackingKey}"
    /// </summary>
    [MaxLength(500)]
    public string? TrackingUrl { get; set; }

    /// <summary>
    /// Sıralama
    /// </summary>
    public int SortOrder { get; set; } = 0;

    public static void Configure(EntityTypeBuilder<ShippingCarrier> builder)
    {
        // StaticId unique olmalı
        builder.HasIndex(sc => sc.StaticId).IsUnique();
        
        // ShortCode unique olmalı
        builder.HasIndex(sc => sc.ShortCode).IsUnique();
        
        // Name unique olmalı
        builder.HasIndex(sc => sc.Name).IsUnique();
    }
}

[Table("ShippingCarriersHistory")]
public class ShippingCarrierHistory : HistoryBaseEntity
{
    // Entity properties
    public int StaticId { get; set; }
    public string Name { get; set; } = null!;
    public string ShortCode { get; set; } = null!;
    public string? Description { get; set; }
    public bool IsImplemented { get; set; } = false;
    public string? ApiUrl { get; set; }
    public string? ApiKey { get; set; }
    public string? Settings { get; set; }
    public string? LogoUrl { get; set; }
    public int SortOrder { get; set; } = 0;
}
