using Application.Contracts.DTOs;
using Core.Enums;

namespace Application.Contracts.Interfaces;

public interface IShipmentService
{
    Task<IEnumerable<ShipmentDto>> GetAllAsync();
    Task<ShipmentDto?> GetByIdAsync(Guid id);
    Task<IEnumerable<ShipmentDto>> GetByOrderIdAsync(Guid orderId);
    Task<IEnumerable<ShipmentDto>> GetByStatusAsync(ShipmentStatus status);
    Task<IEnumerable<ShipmentDto>> SearchAsync(string searchTerm);
    Task<ShipmentDto> CreateAsync(CreateShipmentDto dto);
    Task<ShipmentDto> UpdateAsync(Guid id, UpdateShipmentDto dto);
    Task<bool> DeleteAsync(Guid id);
    Task<bool> UpdateStatusAsync(Guid id, ShipmentStatus status);
    Task<ShipmentDto?> GetByTrackingNumberAsync(string trackingNumber);
}

public class CreateShipmentDto
{
    public Guid OrderId { get; set; }
    public string TrackingNumber { get; set; } = null!;
    public string CargoKey { get; set; } = null!;
    public Guid CarrierId { get; set; }
    public ShipmentStatus Status { get; set; } = ShipmentStatus.Pending;
    public DateTime ShippedAt { get; set; }
    public DateTime? DeliveredAt { get; set; }
    public string? Notes { get; set; }
}

public class UpdateShipmentDto
{
    public string? TrackingNumber { get; set; }
    public string? CargoKey { get; set; }
    public Guid? CarrierId { get; set; }
    public ShipmentStatus? Status { get; set; }
    public DateTime? ShippedAt { get; set; }
    public DateTime? DeliveredAt { get; set; }
    public string? Notes { get; set; }
}
